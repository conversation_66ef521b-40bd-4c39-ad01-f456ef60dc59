package tech.pu12cnt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ResponseCode {
    SUCCESS(200, "成功"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "未找到"),
    INTERNAL_SERVER_ERROR(500, "内部服务器错误"),

    // 用户模块错误码 10000-10999
    USER_NOT_FOUND(10001, "用户不存在"),
    USER_ALREADY_EXISTS(10002, "用户已存在"),
    USER_PASSWORD_ERROR(10003, "密码错误"),
    USER_NAME_ERROR(10004, "用户名错误"),
    USER_DISABLED(10005, "用户已禁用"),
    USER_NOT_LOGIN(10006, "用户未登录"),
    USER_NOT_PERMITTED(10007, "用户无权限"),
    USER_EMAIL_ALREADY_EXISTS(10008, "邮箱已存在"),
    USER_PHONE_ALREADY_EXISTS(10009, "手机号已存在"),

    // JWT相关 40100-40199
    JWT_INVALID(40101, "Token无效"),
    JWT_EXPIRED(40102, "Token已过期"),
    JWT_SIGNATURE_ERROR(40103, "Token签名错误"),
    JWT_MALFORMED(40104, "Token格式错误");

    

    private final Integer code;
    private final String message;
}
