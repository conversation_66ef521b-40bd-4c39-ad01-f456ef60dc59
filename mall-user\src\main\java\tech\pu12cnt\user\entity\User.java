package tech.pu12cnt.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import tech.pu12cnt.common.entity.BaseEntity;

@Data
@TableName("user") // 表名，默认与类名相同，如果不同，需要指定表名
public class User extends BaseEntity {

    private String username;
    private Integer password;
    private String email;
    private String phone;
    private String nickname;
    private String avatar;
    private Integer status;
}
