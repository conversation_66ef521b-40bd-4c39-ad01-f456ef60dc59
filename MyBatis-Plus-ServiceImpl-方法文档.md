# MyBatis-Plus ServiceImpl 完整方法文档

## 📋 目录
1. [保存相关方法](#保存相关方法)
2. [删除相关方法](#删除相关方法)
3. [更新相关方法](#更新相关方法)
4. [查询相关方法](#查询相关方法)
5. [统计相关方法](#统计相关方法)
6. [实用工具方法](#实用工具方法)
7. [最佳实践建议](#最佳实践建议)

---

## 🔄 保存相关方法

### 1. `save(T entity)`
**功能**：保存单个实体（智能保存：ID为空则插入，ID存在则更新）
```java
// 方法签名
boolean save(T entity)

// 使用示例
User newUser = new User();
newUser.setUsername("john");
newUser.setEmail("<EMAIL>");
boolean success = this.save(newUser);  // 插入新用户

// 返回值：true-成功，false-失败
```

### 2. `saveBatch(Collection<T> entityList)`
**功能**：批量保存实体列表
```java
// 方法签名
boolean saveBatch(Collection<T> entityList)

// 使用示例
List<User> users = Arrays.asList(user1, user2, user3);
boolean success = this.saveBatch(users);

// 注意：默认批次大小为1000
```

### 3. `saveBatch(Collection<T> entityList, int batchSize)`
**功能**：批量保存实体列表（指定批次大小）
```java
// 方法签名
boolean saveBatch(Collection<T> entityList, int batchSize)

// 使用示例
List<User> users = getUsers();  // 假设有5000个用户
boolean success = this.saveBatch(users, 500);  // 每批500个

// 适用场景：大量数据导入时，控制批次大小避免内存溢出
```

### 4. `saveOrUpdate(T entity)`
**功能**：保存或更新单个实体（根据ID判断）
```java
// 方法签名
boolean saveOrUpdate(T entity)

// 使用示例
User user = new User();
user.setId(123L);  // ID存在，执行更新
user.setNickname("新昵称");
boolean success = this.saveOrUpdate(user);

User newUser = new User();
newUser.setId(null);  // ID为空，执行插入
newUser.setUsername("jane");
boolean success2 = this.saveOrUpdate(newUser);
```

### 5. `saveOrUpdateBatch(Collection<T> entityList)`
**功能**：批量保存或更新实体列表
```java
// 方法签名
boolean saveOrUpdateBatch(Collection<T> entityList)

// 使用示例
List<User> users = new ArrayList<>();
users.add(existingUser);  // ID有值，会更新
users.add(newUser);       // ID为空，会插入
boolean success = this.saveOrUpdateBatch(users);
```

### 6. `saveOrUpdateBatch(Collection<T> entityList, int batchSize)`
**功能**：批量保存或更新实体列表（指定批次大小）
```java
// 方法签名
boolean saveOrUpdateBatch(Collection<T> entityList, int batchSize)

// 使用示例
boolean success = this.saveOrUpdateBatch(users, 200);
```

---

## 🗑️ 删除相关方法

### 1. `removeById(Serializable id)`
**功能**：根据ID删除实体
```java
// 方法签名
boolean removeById(Serializable id)

// 使用示例
boolean success = this.removeById(123L);  // 删除ID为123的用户
boolean success2 = this.removeById("abc");  // 支持字符串ID
```

### 2. `removeByIds(Collection<? extends Serializable> idList)`
**功能**：根据ID列表批量删除
```java
// 方法签名
boolean removeByIds(Collection<? extends Serializable> idList)

// 使用示例
List<Long> ids = Arrays.asList(123L, 124L, 125L);
boolean success = this.removeByIds(ids);  // 批量删除
```

### 3. `removeByMap(Map<String, Object> columnMap)`
**功能**：根据字段Map删除
```java
// 方法签名
boolean removeByMap(Map<String, Object> columnMap)

// 使用示例
Map<String, Object> map = new HashMap<>();
map.put("status", 0);  // 删除状态为0的用户
map.put("create_time", "2023-01-01");  // 且创建时间为指定日期的
boolean success = this.removeByMap(map);
```

### 4. `remove(Wrapper<T> queryWrapper)`
**功能**：根据条件删除
```java
// 方法签名
boolean remove(Wrapper<T> queryWrapper)

// 使用示例
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.eq("status", 0)  // 状态为0
       .lt("create_time", "2023-01-01");  // 且创建时间早于2023年
boolean success = this.remove(wrapper);
```

---

## 🔄 更新相关方法

### 1. `updateById(T entity)`
**功能**：根据ID更新实体
```java
// 方法签名
boolean updateById(T entity)

// 使用示例
User user = new User();
user.setId(123L);
user.setNickname("新昵称");  // 只更新昵称字段
user.setEmail("<EMAIL>");  // 和邮箱字段
boolean success = this.updateById(user);

// 注意：只更新非null字段
```

### 2. `updateBatchById(Collection<T> entityList)`
**功能**：根据ID批量更新
```java
// 方法签名
boolean updateBatchById(Collection<T> entityList)

// 使用示例
List<User> users = new ArrayList<>();
User user1 = new User();
user1.setId(123L);
user1.setStatus(1);
users.add(user1);

User user2 = new User();
user2.setId(124L);
user2.setStatus(0);
users.add(user2);

boolean success = this.updateBatchById(users);
```

### 3. `updateBatchById(Collection<T> entityList, int batchSize)`
**功能**：根据ID批量更新（指定批次大小）
```java
// 方法签名
boolean updateBatchById(Collection<T> entityList, int batchSize)

// 使用示例
boolean success = this.updateBatchById(users, 100);
```

### 4. `update(Wrapper<T> updateWrapper)`
**功能**：根据条件更新
```java
// 方法签名
boolean update(Wrapper<T> updateWrapper)

// 使用示例
UpdateWrapper<User> wrapper = new UpdateWrapper<>();
wrapper.set("status", 1)  // 设置状态为1
       .set("update_time", new Date())  // 设置更新时间
       .eq("status", 0);  // 条件：当前状态为0
boolean success = this.update(wrapper);
```

### 5. `update(T entity, Wrapper<T> updateWrapper)`
**功能**：根据条件更新实体
```java
// 方法签名
boolean update(T entity, Wrapper<T> updateWrapper)

// 使用示例
User user = new User();
user.setNickname("批量昵称");
user.setStatus(1);

QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.eq("status", 0);  // 条件：状态为0的用户

boolean success = this.update(user, wrapper);  // 将所有状态为0的用户更新
```

---

## 🔍 查询相关方法

### 1. `getById(Serializable id)`
**功能**：根据ID查询单个实体
```java
// 方法签名
T getById(Serializable id)

// 使用示例
User user = this.getById(123L);  // 查询ID为123的用户
if (user != null) {
    System.out.println(user.getUsername());
}
```

### 2. `listByIds(Collection<? extends Serializable> idList)`
**功能**：根据ID列表查询
```java
// 方法签名
Collection<T> listByIds(Collection<? extends Serializable> idList)

// 使用示例
List<Long> ids = Arrays.asList(123L, 124L, 125L);
Collection<User> users = this.listByIds(ids);
```

### 3. `listByMap(Map<String, Object> columnMap)`
**功能**：根据字段Map查询
```java
// 方法签名
Collection<T> listByMap(Map<String, Object> columnMap)

// 使用示例
Map<String, Object> map = new HashMap<>();
map.put("status", 1);  // 状态为1
map.put("username", "admin");  // 用户名为admin
Collection<User> users = this.listByMap(map);
```

### 4. `getOne(Wrapper<T> queryWrapper)`
**功能**：根据条件查询一个实体
```java
// 方法签名
T getOne(Wrapper<T> queryWrapper)

// 使用示例
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.eq("username", "admin");
User user = this.getOne(wrapper);

// 注意：如果查询结果多于1个，会抛出异常
```

### 5. `getOne(Wrapper<T> queryWrapper, boolean throwEx)`
**功能**：根据条件查询一个实体（控制是否抛异常）
```java
// 方法签名
T getOne(Wrapper<T> queryWrapper, boolean throwEx)

// 使用示例
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.like("username", "admin");
User user = this.getOne(wrapper, false);  // 多个结果时不抛异常，返回第一个
```

### 6. `list()`
**功能**：查询所有实体
```java
// 方法签名
List<T> list()

// 使用示例
List<User> allUsers = this.list();  // 查询所有用户

// 注意：数据量大时慎用
```

### 7. `list(Wrapper<T> queryWrapper)`
**功能**：根据条件查询实体列表
```java
// 方法签名
List<T> list(Wrapper<T> queryWrapper)

// 使用示例
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.eq("status", 1)  // 状态为1
       .orderByDesc("create_time");  // 按创建时间倒序
List<User> users = this.list(wrapper);
```

### 8. `page(IPage<T> page)`
**功能**：分页查询所有实体
```java
// 方法签名
IPage<T> page(IPage<T> page)

// 使用示例
Page<User> page = new Page<>(1, 10);  // 第1页，每页10条
IPage<User> result = this.page(page);

System.out.println("总记录数：" + result.getTotal());
System.out.println("总页数：" + result.getPages());
List<User> users = result.getRecords();
```

### 9. `page(IPage<T> page, Wrapper<T> queryWrapper)`
**功能**：根据条件分页查询
```java
// 方法签名
IPage<T> page(IPage<T> page, Wrapper<T> queryWrapper)

// 使用示例
Page<User> page = new Page<>(1, 10);
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.eq("status", 1);

IPage<User> result = this.page(page, wrapper);
```

### 10. `listMaps()`
**功能**：查询所有记录，返回Map列表
```java
// 方法签名
List<Map<String, Object>> listMaps()

// 使用示例
List<Map<String, Object>> maps = this.listMaps();
for (Map<String, Object> map : maps) {
    System.out.println("用户名：" + map.get("username"));
    System.out.println("邮箱：" + map.get("email"));
}
```

### 11. `listMaps(Wrapper<T> queryWrapper)`
**功能**：根据条件查询，返回Map列表
```java
// 方法签名
List<Map<String, Object>> listMaps(Wrapper<T> queryWrapper)

// 使用示例
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.select("username", "email")  // 只查询指定字段
       .eq("status", 1);
List<Map<String, Object>> maps = this.listMaps(wrapper);
```

### 12. `listObjs()`
**功能**：查询所有记录的第一个字段值
```java
// 方法签名
List<Object> listObjs()

// 使用示例
List<Object> ids = this.listObjs();  // 返回所有用户的ID列表
```

### 13. `listObjs(Function<? super Object, V> mapper)`
**功能**：查询所有记录的第一个字段值并转换
```java
// 方法签名
<V> List<V> listObjs(Function<? super Object, V> mapper)

// 使用示例
List<String> idStrings = this.listObjs(obj -> obj.toString());
```

### 14. `listObjs(Wrapper<T> queryWrapper)`
**功能**：根据条件查询第一个字段值
```java
// 方法签名
List<Object> listObjs(Wrapper<T> queryWrapper)

// 使用示例
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.select("username")  // 只查询用户名字段
       .eq("status", 1);
List<Object> usernames = this.listObjs(wrapper);
```

---

## 📊 统计相关方法

### 1. `count()`
**功能**：统计所有记录数
```java
// 方法签名
int count()

// 使用示例
int totalUsers = this.count();  // 统计总用户数
System.out.println("总用户数：" + totalUsers);
```

### 2. `count(Wrapper<T> queryWrapper)`
**功能**：根据条件统计记录数
```java
// 方法签名
int count(Wrapper<T> queryWrapper)

// 使用示例
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.eq("status", 1);  // 状态为1的用户
int activeUsers = this.count(wrapper);

QueryWrapper<User> wrapper2 = new QueryWrapper<>();
wrapper2.like("email", "@qq.com");  // 邮箱包含@qq.com的用户
int qqUsers = this.count(wrapper2);
```

---

## 🛠️ 实用工具方法

### 1. `getMap(Wrapper<T> queryWrapper)`
**功能**：根据条件查询一条记录，返回Map
```java
// 方法签名
Map<String, Object> getMap(Wrapper<T> queryWrapper)

// 使用示例
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.eq("username", "admin");
Map<String, Object> userMap = this.getMap(wrapper);
```

### 2. `getObj(Wrapper<T> queryWrapper, Function<? super Object, V> mapper)`
**功能**：根据条件查询一个字段值并转换
```java
// 方法签名
<V> V getObj(Wrapper<T> queryWrapper, Function<? super Object, V> mapper)

// 使用示例
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.select("username")
       .eq("id", 123L);
String username = this.getObj(wrapper, obj -> obj.toString());
```

---

## 💡 最佳实践建议

### 1. **性能优化建议**

#### **使用count()而不是list().size()**
```java
// ❌ 性能差：查询所有数据再统计
int count = this.list(wrapper).size();

// ✅ 性能好：只统计数量
int count = this.count(wrapper);
```

#### **分页查询大量数据**
```java
// ❌ 一次查询所有数据
List<User> allUsers = this.list();

// ✅ 分页查询
Page<User> page = new Page<>(1, 100);
IPage<User> result = this.page(page);
```

#### **只查询需要的字段**
```java
// ❌ 查询所有字段
List<User> users = this.list(wrapper);

// ✅ 只查询需要的字段
QueryWrapper<User> wrapper = new QueryWrapper<>();
wrapper.select("id", "username", "email");
List<Map<String, Object>> users = this.listMaps(wrapper);
```

### 2. **批量操作建议**

#### **大量数据时控制批次大小**
```java
// ✅ 控制批次大小，避免内存溢出
this.saveBatch(users, 500);  // 每批500条
```

#### **批量操作优于循环单个操作**
```java
// ❌ 循环单个操作
for (User user : users) {
    this.save(user);
}

// ✅ 批量操作
this.saveBatch(users);
```

### 3. **异常处理建议**

#### **检查查询结果**
```java
// ✅ 检查结果是否为空
User user = this.getById(id);
if (user == null) {
    throw new BusinessException("用户不存在");
}
```

#### **使用getOne时注意多结果异常**
```java
// ✅ 控制异常抛出
User user = this.getOne(wrapper, false);  // 多结果时不抛异常
```

### 4. **常用组合查询模板**

#### **分页+条件查询**
```java
public IPage<User> searchUsers(int page, int size, String keyword, Integer status) {
    Page<User> pageObj = new Page<>(page, size);
    QueryWrapper<User> wrapper = new QueryWrapper<>();

    if (StringUtils.isNotBlank(keyword)) {
        wrapper.and(w -> w.like("username", keyword)
                         .or().like("nickname", keyword)
                         .or().like("email", keyword));
    }

    if (status != null) {
        wrapper.eq("status", status);
    }

    wrapper.orderByDesc("create_time");

    return this.page(pageObj, wrapper);
}
```

#### **统计查询**
```java
public Map<String, Integer> getUserStatistics() {
    Map<String, Integer> stats = new HashMap<>();

    // 总用户数
    stats.put("total", this.count());

    // 活跃用户数
    stats.put("active", this.count(new QueryWrapper<User>().eq("status", 1)));

    // 今日注册用户数
    stats.put("todayRegister", this.count(
        new QueryWrapper<User>().ge("create_time", LocalDate.now())
    ));

    return stats;
}
```

---

## 📝 总结

MyBatis-Plus的ServiceImpl提供了丰富的CRUD方法，让开发者可以：

1. **快速开发**：无需编写大量SQL语句
2. **类型安全**：编译时检查，减少运行时错误
3. **性能优化**：提供批量操作、分页查询等高效方法
4. **灵活查询**：支持复杂的条件构造和动态查询

**使用建议**：
- 优先使用ServiceImpl提供的方法
- 复杂业务逻辑时才考虑自定义SQL
- 注意性能优化，合理使用批量操作和分页查询
- 做好异常处理和参数校验

这份文档涵盖了ServiceImpl的所有主要方法，建议收藏备用！
