package tech.pu12cnt.user.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tech.pu12cnt.common.result.Result;

@RestController
@RequestMapping("/api/user")
public class UserController {
    @Autowired
    private UserService UserService;

    @PostMapping("/register")
    public Result<User> register(@Valid @RequestBody User user) {
        User registeredUser = UserService.register(user);
        return Result.success(registeredUser);
    }

    @PostMapping("/login")
    public Result<String> login(@RequestBody LoginRequest request) {
        String token = UserService.login(request.getUsername(), request.getPassword());
        return Result.success(token);
    }

    @GetMapping("/info/{id}")
    public Result<User> getUserInfo(@PathVariable Long id) {
        User user = UserService.getById(id);
        return Result.success(user);
    }

    @PutMapping("/update")
    public Result<Void> updateUserInfo(@Valid @RequestBody User user) {
        UserService.updateById(user);
        return Result.success();
    }
    
}