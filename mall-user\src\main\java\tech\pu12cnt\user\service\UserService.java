package tech.pu12cnt.user.service;
import tech.pu12cnt.user.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;

public interface UserService extends IService<User>{
/**
 * 用户注册
 * @param newUser 新用户信息
 * @return 注册成功的用户信息
 */
    User register(User newUser);
/**
 * 用户登录
 * @param username 用户名
 * @param password 密码
 * @return JWT Token
 */
    String login(String username, String password);

    User getUserByUsername(String username);

    boolean existsByUsername(String username);

    boolean existsByEmail(String email);
    
    boolean existsByPhone(String phone);


}