package tech.pu12cnt.user.service;

import tech.pu12cnt.user.entity.User;
import tech.pu12cnt.user.mapper.UserMapper;
import tech.pu12cnt.common.enums.ResponseCode;
import tech.pu12cnt.common.exception.BusinessException;
import tech.pu12cnt.common.utils.JwtUtils;


import com.baomidou.mybatisplus.extension.service.IService;

public interface UserService extends IService<User>{


    public User register(User newUser){
        // 1.校验用户名是否已存在
        // 2.校验邮箱是否已存在
        // 3.校验手机号是否已存在
        // 4.密码加密
        // 5.保存用户信息
        User existingUser = userMapper.findByUsername(newUser.getUsername());

        if(existingUser != null){
            throw new RuntimeException(ResponseCode.USER_ALREADY_EXISTS);
        }else if(userMapper.findByEmail(newUser.getEmail()) != null){
            throw new RuntimeException(ResponseCode.USER_ALREADY_EXISTS);
        }else if(userMapper.findByPhone(newUser.getPhone()) != null){
            throw new RuntimeException(ResponseCode.USER_ALREADY_EXISTS);
        }else{
            // 4.密码加密
            // 5.保存用户信息
            newUser.setPassword(passwordEncoder.encode(newUser.getPassword()));
            userMapper.insert(newUser);
            return newUser;
        }

    };

    public String login(String username, String password){
        // 1.校验用户名是否存在
        // 2.校验密码是否正确
        // 3.生成Token
        User user = userMapper.findByUsername(username);
        if(user == null){
            throw new RuntimeException(ResponseCode.USER_NOT_FOUND);
        }else if(!passwordEncoder.matches(password, user.getPassword())){
            throw new RuntimeException(ResponseCode.USER_PASSWORD_ERROR);
        }else{
            // 3.生成Token
            return JwtUtils.generateToken(user.getId(), user.getUsername());
        }

    };
    

}