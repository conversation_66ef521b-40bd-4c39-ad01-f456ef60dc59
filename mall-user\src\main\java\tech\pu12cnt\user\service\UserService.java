package tech.pu12cnt.user.service;
import tech.pu12cnt.user.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;

public interface UserService extends IService<User>{

    String register(User newUser);

    String login(String username, String password);

    User getUserByUsername(String username);
    boolean existsByUsername(String username);
    boolean existsByEmail(String email);
    boolean existsByPhone(String phone);


}