package tech.pu12cnt.user.service.impl;

import org.springframework.stereotype.Service;

@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService{

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public User register(User newUser) {
        // 检查用户名是否已存在
        if (existsByUsername(newUser.getUsername())) {
            throw new BusinessException(ResponseCode.USER_ALREADY_EXISTS);
        }else if(existsByEmail(newUser.getEmail())){
            throw new BusinessException(ResponseCode.USER_ALREADY_EXISTS);
        }else if(existsByPhone(newUser.getPhone())){
            throw new BusinessException(ResponseCode.USER_ALREADY_EXISTS);
        }else{
            newUser.setPassword(passwordEncoder.encode(newUser.getPassword()));
            userMapper.insert(newUser);
            return newUser;
        }
    }

    @Override
    public String login(String username, String password) {
        // 1. 判断用户是否存在
        if (existsByUsername(username)) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }
        // 3. 判断密码是否正确
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BusinessException(ResponseCode.USER_PASSWORD_ERROR);
        }
        // 4. 生成JWT Token
        return JwtUtils.generateToken(user.getId(), user.getUsername());
    }

    @Override
    public User getUserByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userMapper.findByUsername(username) != null;
    }

    @Override
    public boolean existsByEmail(String email) {
        return userMapper.findByEmail(email) != null;
    }

    @Override
    public boolean existsByPhone(String phone) {
        return userMapper.findByPhone(phone) != null;
    }


}